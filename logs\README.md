# Logs 日志文件目录

## 目录说明

本目录用于存储 MartingaleEA 面向对象重构项目运行时产生的所有日志文件。

## 日志文件类型

### 1. 主日志文件
- **MartingaleEA_YYYYMMDD.log** - 主要运行日志
- **MartingaleEA_Error_YYYYMMDD.log** - 错误日志
- **MartingaleEA_Debug_YYYYMMDD.log** - 调试日志

### 2. 模块日志文件
- **Trading_YYYYMMDD.log** - 交易操作日志
- **Risk_YYYYMMDD.log** - 风险控制日志
- **Grid_YYYYMMDD.log** - 网格管理日志
- **Orders_YYYYMMDD.log** - 订单管理日志

### 3. 性能日志文件
- **Performance_YYYYMMDD.log** - 性能监控日志
- **Statistics_YYYYMMDD.log** - 统计信息日志

### 4. 审计日志文件
- **Audit_YYYYMMDD.log** - 审计跟踪日志
- **Config_Changes_YYYYMMDD.log** - 配置变更日志

## 日志级别说明

### 1. ERROR (错误级别)
- **用途**: 记录系统错误和异常情况
- **示例**: 订单操作失败、初始化错误、严重异常
- **格式**: `[ERROR] [时间戳] [模块] 错误描述`

### 2. WARNING (警告级别)
- **用途**: 记录潜在问题和警告信息
- **示例**: 风险警告、配置问题、性能警告
- **格式**: `[WARNING] [时间戳] [模块] 警告描述`

### 3. INFO (信息级别)
- **用途**: 记录重要的业务操作和状态变化
- **示例**: 交易执行、策略切换、系统状态
- **格式**: `[INFO] [时间戳] [模块] 信息描述`

### 4. DEBUG (调试级别)
- **用途**: 记录详细的调试信息和执行流程
- **示例**: 方法调用、参数值、内部状态
- **格式**: `[DEBUG] [时间戳] [模块] 调试信息`

## 日志文件格式

### 1. 标准日志格式
```
[级别] [YYYY-MM-DD HH:MM:SS.mmm] [模块名] [线程ID] 日志消息
```

### 2. 交易日志格式
```
[TRADE] [YYYY-MM-DD HH:MM:SS.mmm] [操作类型] 订单号:12345 类型:BUY 手数:0.01 价格:1.2345 结果:成功
```

### 3. 错误日志格式
```
[ERROR] [YYYY-MM-DD HH:MM:SS.mmm] [模块名] 错误代码:10001 错误描述:订单开仓失败 详细信息:余额不足
```

### 4. 性能日志格式
```
[PERF] [YYYY-MM-DD HH:MM:SS.mmm] [方法名] 执行时间:15ms 内存使用:2.5MB CPU使用:5%
```

## 日志轮转策略

### 1. 按时间轮转
- **日轮转**: 每天创建新的日志文件
- **周轮转**: 每周创建新的日志文件
- **月轮转**: 每月创建新的日志文件

### 2. 按大小轮转
- **文件大小限制**: 单个日志文件最大 10MB
- **自动轮转**: 超过大小限制时自动创建新文件
- **文件命名**: 添加序号后缀 (例如: MartingaleEA_20241219_001.log)

### 3. 历史文件管理
- **保留期限**: 默认保留 30 天的日志文件
- **压缩存储**: 超过 7 天的日志文件自动压缩
- **自动清理**: 超过保留期限的文件自动删除

## 日志配置示例

### 1. 生产环境配置
```ini
[Logging]
EnableFileLogging=true
LogLevel=1
; 只记录警告和错误
EnableRotation=true
MaxLogFileSize=10485760
; 10MB
MaxLogFiles=30
; 保留30个文件
```

### 2. 开发环境配置
```ini
[Logging]
EnableFileLogging=true
LogLevel=3
; 记录所有级别
EnableDebugMode=true
EnableRotation=false
MaxLogFileSize=52428800
; 50MB
```

### 3. 测试环境配置
```ini
[Logging]
EnableFileLogging=true
LogLevel=2
; 记录信息、警告和错误
EnableRotation=true
MaxLogFileSize=5242880
; 5MB
MaxLogFiles=10
```

## 日志文件示例

### 1. 主日志文件示例
```
[INFO] [2024-12-19 10:00:00.123] [MartingaleEA] [Main] EA初始化开始
[INFO] [2024-12-19 10:00:00.145] [ConfigManager] [Main] 配置文件加载成功: default.ini
[INFO] [2024-12-19 10:00:00.167] [Logger] [Main] 日志系统初始化完成
[INFO] [2024-12-19 10:00:00.189] [OrderManager] [Main] 订单管理器初始化完成
[INFO] [2024-12-19 10:00:00.201] [RiskController] [Main] 风险控制器初始化完成
[INFO] [2024-12-19 10:00:00.223] [MartingaleStrategy] [Main] 策略初始化完成
[INFO] [2024-12-19 10:00:00.245] [MartingaleEA] [Main] EA初始化成功，开始交易
```

### 2. 交易日志文件示例
```
[TRADE] [2024-12-19 10:15:30.456] [OPEN] 订单号:12345 类型:BUY 手数:0.01 价格:1.2345 止损:1.2325 止盈:1.2395 结果:成功
[TRADE] [2024-12-19 10:25:45.789] [OPEN] 订单号:12346 类型:BUY 手数:0.02 价格:1.2315 止损:1.2295 止盈:1.2365 结果:成功
[TRADE] [2024-12-19 10:35:12.234] [CLOSE] 订单号:12345 类型:BUY 手数:0.01 平仓价:1.2395 盈亏:+5.00 结果:成功
```

### 3. 错误日志文件示例
```
[ERROR] [2024-12-19 10:45:30.567] [OrderManager] [Trade] 错误代码:134 错误描述:资金不足 订单详情:BUY 0.05 EURUSD
[ERROR] [2024-12-19 10:50:15.890] [RiskController] [Risk] 错误代码:10005 错误描述:风险限制超出 当前回撤:25.5% 最大允许:20.0%
[ERROR] [2024-12-19 11:00:45.123] [MartingaleStrategy] [Strategy] 错误代码:10001 错误描述:参数验证失败 参数:InitialLots=0.001
```

### 4. 调试日志文件示例
```
[DEBUG] [2024-12-19 10:00:01.001] [MartingaleStrategy] [Execute] 方法调用开始
[DEBUG] [2024-12-19 10:00:01.002] [MartingaleStrategy] [Execute] 检查入场条件
[DEBUG] [2024-12-19 10:00:01.003] [RiskController] [CheckRisk] 当前回撤:5.2% 保证金水平:450%
[DEBUG] [2024-12-19 10:00:01.004] [GridManager] [OptimalEntry] 计算最优入场点 当前价格:1.2345
[DEBUG] [2024-12-19 10:00:01.005] [MartingaleStrategy] [Execute] 入场条件满足，准备开仓
[DEBUG] [2024-12-19 10:00:01.006] [OrderManager] [OpenOrder] 开仓参数 类型:BUY 手数:0.01 价格:1.2345
[DEBUG] [2024-12-19 10:00:01.007] [MartingaleStrategy] [Execute] 方法调用结束 结果:true
```

## 日志分析工具

### 1. 日志查看命令
```bash
# 查看最新日志
tail -f MartingaleEA_20241219.log

# 查看错误日志
grep "ERROR" MartingaleEA_20241219.log

# 查看交易日志
grep "TRADE" MartingaleEA_20241219.log

# 统计错误数量
grep -c "ERROR" MartingaleEA_20241219.log
```

### 2. 日志过滤示例
```bash
# 查看特定时间段的日志
grep "10:00:00" MartingaleEA_20241219.log

# 查看特定模块的日志
grep "OrderManager" MartingaleEA_20241219.log

# 查看特定级别的日志
grep "\[WARNING\]" MartingaleEA_20241219.log
```

## 日志监控和告警

### 1. 错误监控
- **错误频率**: 监控错误日志的产生频率
- **错误类型**: 统计不同类型错误的分布
- **错误趋势**: 分析错误发生的趋势变化

### 2. 性能监控
- **响应时间**: 监控关键操作的响应时间
- **资源使用**: 监控内存和CPU使用情况
- **吞吐量**: 监控交易处理的吞吐量

### 3. 业务监控
- **交易统计**: 监控交易成功率和盈亏情况
- **风险指标**: 监控回撤和保证金水平
- **系统状态**: 监控EA的运行状态

## 日志安全和隐私

### 1. 敏感信息保护
- **密码屏蔽**: 不记录密码等敏感信息
- **账户信息**: 对账户信息进行脱敏处理
- **个人数据**: 遵循数据保护法规

### 2. 访问控制
- **文件权限**: 设置适当的文件访问权限
- **用户权限**: 限制日志文件的访问用户
- **网络安全**: 保护日志传输的安全性

### 3. 数据完整性
- **校验和**: 为日志文件生成校验和
- **数字签名**: 对重要日志进行数字签名
- **备份策略**: 定期备份重要日志文件

## 维护指南

### 1. 定期维护
- **清理过期日志**: 定期清理超过保留期的日志文件
- **压缩归档**: 压缩长期保存的日志文件
- **磁盘空间**: 监控日志目录的磁盘空间使用

### 2. 故障排查
- **日志分析**: 通过日志分析定位问题
- **错误追踪**: 跟踪错误的产生和传播路径
- **性能诊断**: 通过性能日志诊断性能问题

### 3. 优化建议
- **日志级别**: 根据环境调整合适的日志级别
- **轮转策略**: 根据使用情况调整轮转策略
- **存储优化**: 优化日志存储和检索性能
